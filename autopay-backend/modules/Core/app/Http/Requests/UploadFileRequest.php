<?php

namespace Modules\Core\Http\Requests;

class UploadFileRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'image',
                'mimes:jpeg,jpg,png,gif,webp,svg',
                'max:5120', // 5MB in kilobytes
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'Vui lòng chọn file để upload.',
            'file.file' => 'File upload không hợp lệ.',
            'file.image' => 'File phải là hình ảnh.',
            'file.mimes' => 'File phải có định dạng: jpeg, jpg, png, gif, webp, svg.',
            'file.max' => 'Kích thước file không được vượt quá 5MB.',
        ];
    }
}
